package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var PMOProject = repository.Make[models.PMOProject]()

func PMOProjectOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOProjectWithSearch(q string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ? OR slug ILIKE ? OR email ILIKE ?", searchTerm, searchTerm, searchTerm)
	}
}

func PMOProjectWithStatus(status string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if status == "" {
			return
		}
		c.Where("status = ?", status)
	}
}

func PMOProjectWithRelations(includeAll bool) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		c.Preload("Project").Preload("CreatedBy").Preload("UpdatedBy").Preload("ContractInfo")
		if includeAll {
			c.Preload("BiddingInfo").
				Preload("Collaborators").
				Preload("BudgetInfo").
				Preload("BidbondInfo").
				Preload("LGInfo").
				Preload("Remarks")
		}
	}
}

func PMOProjectWithCurrentPermission(userID string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if userID == "" {
			return
		}
		c.Preload("Permission", "user_id = ?", userID)
	}
}

func PMOProjectByOwnerPermission(userID string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if userID == "" {
			return
		}

		// Check if user has SUPER or ADMIN access level for PMO
		// If they do, they can see all projects
		c.Where(`
			id IN (
				SELECT p.id FROM pmo_projects p
				WHERE EXISTS (
					SELECT 1 FROM user_access_levels ual
					WHERE ual.user_id = ?
					AND ual.pmo IN ('SUPER')
				)
				OR EXISTS (
					SELECT 1 FROM pmo_collaborators pc
					WHERE pc.project_id = p.id
					AND pc.user_id = ? AND pc.deleted_at IS NULL
				)
				OR p.created_by_id = ?
			)
		`, userID, userID, userID)
	}
}
